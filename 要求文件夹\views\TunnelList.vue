<template>
  <div class="tunnel-list">
    <div class="page-header">
      <h2>隧道列表</h2>
      <n-button type="primary" @click="$router.push({ name: 'tunnel-create' })">
        <n-icon size="16">
          <AddOutline />
        </n-icon>
        创建隧道
      </n-button>
    </div>

    <!-- 筛选和搜索 -->
    <n-card :bordered="false" class="filter-card">
      <n-space :vertical="isMobile" :size="16">
        <n-input
          v-model:value="searchText"
          placeholder="搜索隧道名称或备注"
          clearable
          style="width: 200px;"
        >
          <template #prefix>
            <n-icon>
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>
        
        <n-select
          v-model:value="statusFilter"
          placeholder="状态筛选"
          :options="statusOptions"
          clearable
          style="width: 120px;"
        />
        
        <n-select
          v-model:value="typeFilter"
          placeholder="类型筛选"
          :options="typeOptions"
          clearable
          style="width: 120px;"
        />
        
        <n-button @click="refreshTunnels" :loading="loading">
          <n-icon size="16">
            <RefreshOutline />
          </n-icon>
          刷新
        </n-button>
      </n-space>
    </n-card>

    <!-- 隧道列表 -->
    <n-card :bordered="false">
      <n-data-table
        :columns="columns"
        :data="filteredTunnels"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row: any) => row.id"
        :scroll-x="isMobile ? 800 : undefined"
      />
    </n-card>

    <!-- 编辑隧道弹窗 -->
    <n-modal v-model:show="showEditModal" preset="card" title="编辑隧道" style="width: 600px;">
      <n-form ref="editFormRef" :model="editForm" :rules="editRules" label-placement="left" label-width="100px">
        <n-form-item path="name" label="隧道名称">
          <n-input v-model:value="editForm.name" placeholder="请输入隧道名称" />
        </n-form-item>
        <n-form-item path="localIp" label="本地IP">
          <n-input v-model:value="editForm.localIp" placeholder="请输入本地IP地址" />
        </n-form-item>
        <n-form-item path="localPort" label="本地端口">
          <n-input-number v-model:value="editForm.localPort" placeholder="请输入本地端口" :min="1" :max="65535" />
        </n-form-item>
        <n-form-item path="remotePort" label="远程端口">
          <n-input-number v-model:value="editForm.remotePort" placeholder="请输入远程端口" :min="1" :max="65535" />
        </n-form-item>
        <n-form-item path="remark" label="备注">
          <n-input
            v-model:value="editForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="handleUpdateTunnel" :loading="updating">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 删除确认弹窗 -->
    <n-modal v-model:show="showDeleteModal" preset="dialog" type="warning">
      <template #header>
        <div>确认删除</div>
      </template>
      <div>
        确定要删除隧道 <strong>{{ deleteTarget?.name }}</strong> 吗？此操作不可撤销。
      </div>
      <template #action>
        <n-button @click="showDeleteModal = false">取消</n-button>
        <n-button type="error" @click="handleDeleteTunnel" :loading="updating">删除</n-button>
      </template>
    </n-modal>

    <!-- 配置文件弹窗 -->
    <n-modal v-model:show="showConfigModal" preset="card" title="配置文件" style="width: 700px;">
      <n-alert type="info" style="margin-bottom: 16px;">
        请将以下配置保存为 frpc.ini 文件，并使用客户端加载
      </n-alert>
      <n-code
        :code="configContent"
        language="ini"
        show-line-numbers
        style="max-height: 400px; overflow-y: auto;"
      />
      <template #footer>
        <n-space justify="end">
          <n-button @click="copyConfig">复制配置</n-button>
          <n-button type="primary" @click="downloadConfig">下载配置</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage } from 'naive-ui'
import { 
  AddOutline, 
  SearchOutline, 
  RefreshOutline,
  PlayOutline,
  StopOutline,
  CreateOutline,
  TrashOutline,
  DocumentTextOutline,
  EyeOutline
} from '@vicons/ionicons5'

const message = useMessage()

// 响应式检测
const isMobile = ref(window.innerWidth <= 768)

// 数据状态
const loading = ref(false)
const updating = ref(false)
const tunnels = ref([])
const searchText = ref('')
const statusFilter = ref(null)
const typeFilter = ref(null)

// 弹窗状态
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const showConfigModal = ref(false)

// 编辑表单
const editForm = ref({
  id: '',
  name: '',
  localIp: '',
  localPort: null,
  remotePort: null,
  remark: ''
})

const deleteTarget = ref(null)
const configContent = ref('')

// 筛选选项
const statusOptions = [
  { label: '运行中', value: 'running' },
  { label: '已停止', value: 'stopped' },
  { label: '错误', value: 'error' }
]

const typeOptions = [
  { label: 'HTTP', value: 'http' },
  { label: 'HTTPS', value: 'https' },
  { label: 'TCP', value: 'tcp' },
  { label: 'UDP', value: 'udp' }
]

// 表格列定义
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    fixed: 'left'
  },
  {
    title: '隧道名称',
    key: 'name',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    render: (row: any) => {
      const typeMap = {
        http: { type: 'info', text: 'HTTP' },
        https: { type: 'success', text: 'HTTPS' },
        tcp: { type: 'warning', text: 'TCP' },
        udp: { type: 'error', text: 'UDP' }
      }
      const config = typeMap[row.type] || { type: 'default', text: row.type }
      return h('n-tag', { type: config.type, size: 'small' }, { default: () => config.text })
    }
  },
  {
    title: '本地地址',
    key: 'localAddress',
    width: 150,
    render: (row: any) => `${row.localIp}:${row.localPort}`
  },
  {
    title: '远程端口',
    key: 'remotePort',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: any) => {
      const statusMap = {
        running: { type: 'success', text: '运行中' },
        stopped: { type: 'default', text: '已停止' },
        error: { type: 'error', text: '错误' }
      }
      const config = statusMap[row.status] || { type: 'default', text: '未知' }
      return h('n-tag', { type: config.type, size: 'small' }, { default: () => config.text })
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 150,
    render: (row: any) => new Date(row.createTime).toLocaleString('zh-CN')
  },
  {
    title: '备注',
    key: 'remark',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: any) => {
      return h('n-space', { size: 'small' }, {
        default: () => [
          h('n-button', {
            size: 'small',
            type: row.status === 'running' ? 'error' : 'success',
            ghost: true,
            onClick: () => toggleTunnelStatus(row)
          }, {
            default: () => row.status === 'running' ? '停止' : '启动',
            icon: () => h('n-icon', { size: 14 }, {
              default: () => h(row.status === 'running' ? StopOutline : PlayOutline)
            })
          }),
          h('n-button', {
            size: 'small',
            type: 'info',
            ghost: true,
            onClick: () => editTunnel(row)
          }, {
            default: () => '编辑',
            icon: () => h('n-icon', { size: 14 }, { default: () => h(CreateOutline) })
          }),
          h('n-button', {
            size: 'small',
            type: 'warning',
            ghost: true,
            onClick: () => showConfig(row)
          }, {
            default: () => '配置',
            icon: () => h('n-icon', { size: 14 }, { default: () => h(DocumentTextOutline) })
          }),
          h('n-button', {
            size: 'small',
            type: 'error',
            ghost: true,
            onClick: () => deleteTunnel(row)
          }, {
            default: () => '删除',
            icon: () => h('n-icon', { size: 14 }, { default: () => h(TrashOutline) })
          })
        ]
      })
    }
  }
]

// 分页配置
const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: (info: any) => `共 ${info.itemCount} 条`
}

// 过滤后的隧道列表
const filteredTunnels = computed(() => {
  let result = tunnels.value

  if (searchText.value) {
    result = result.filter((tunnel: any) =>
      tunnel.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
      (tunnel.remark && tunnel.remark.toLowerCase().includes(searchText.value.toLowerCase()))
    )
  }

  if (statusFilter.value) {
    result = result.filter((tunnel: any) => tunnel.status === statusFilter.value)
  }

  if (typeFilter.value) {
    result = result.filter((tunnel: any) => tunnel.type === typeFilter.value)
  }

  return result
})

// 表单验证规则
const editRules = {
  name: {
    required: true,
    message: '请输入隧道名称',
    trigger: 'blur'
  },
  localIp: {
    required: true,
    message: '请输入本地IP地址',
    trigger: 'blur'
  },
  localPort: {
    required: true,
    type: 'number',
    message: '请输入本地端口',
    trigger: 'blur'
  },
  remotePort: {
    required: true,
    type: 'number',
    message: '请输入远程端口',
    trigger: 'blur'
  }
}

// 刷新隧道列表
const refreshTunnels = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    tunnels.value = [
      {
        id: 1,
        name: 'web-server',
        type: 'http',
        localIp: '127.0.0.1',
        localPort: 8080,
        remotePort: 80,
        status: 'running',
        createTime: '2024-01-01T10:00:00Z',
        remark: 'Web服务器'
      },
      {
        id: 2,
        name: 'ssh-server',
        type: 'tcp',
        localIp: '*************',
        localPort: 22,
        remotePort: 2222,
        status: 'stopped',
        createTime: '2024-01-02T10:00:00Z',
        remark: 'SSH远程连接'
      }
    ]
  } catch (error) {
    message.error('加载隧道列表失败')
  } finally {
    loading.value = false
  }
}

// 切换隧道状态
const toggleTunnelStatus = async (tunnel: any) => {
  updating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    tunnel.status = tunnel.status === 'running' ? 'stopped' : 'running'
    message.success(`隧道已${tunnel.status === 'running' ? '启动' : '停止'}`)
  } catch (error) {
    message.error('操作失败')
  } finally {
    updating.value = false
  }
}

// 编辑隧道
const editTunnel = (tunnel: any) => {
  editForm.value = { ...tunnel }
  showEditModal.value = true
}

// 更新隧道
const handleUpdateTunnel = async () => {
  updating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    const index = tunnels.value.findIndex((t: any) => t.id === editForm.value.id)
    if (index !== -1) {
      tunnels.value[index] = { ...editForm.value }
    }
    message.success('隧道更新成功')
    showEditModal.value = false
  } catch (error) {
    message.error('更新失败')
  } finally {
    updating.value = false
  }
}

// 删除隧道
const deleteTunnel = (tunnel: any) => {
  deleteTarget.value = tunnel
  showDeleteModal.value = true
}

// 确认删除隧道
const handleDeleteTunnel = async () => {
  updating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    tunnels.value = tunnels.value.filter((t: any) => t.id !== deleteTarget.value.id)
    message.success('隧道删除成功')
    showDeleteModal.value = false
  } catch (error) {
    message.error('删除失败')
  } finally {
    updating.value = false
  }
}

// 显示配置文件
const showConfig = (tunnel: any) => {
  configContent.value = `[common]
server_addr = frp.example.com
server_port = 7000
token = your_token_here

[${tunnel.name}]
type = ${tunnel.type}
local_ip = ${tunnel.localIp}
local_port = ${tunnel.localPort}
remote_port = ${tunnel.remotePort}`

  showConfigModal.value = true
}

// 复制配置
const copyConfig = async () => {
  try {
    await navigator.clipboard.writeText(configContent.value)
    message.success('配置已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 下载配置
const downloadConfig = () => {
  const blob = new Blob([configContent.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'frpc.ini'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  message.success('配置文件下载成功')
}

onMounted(() => {
  refreshTunnels()

  // 监听窗口大小变化
  const handleResize = () => {
    isMobile.value = window.innerWidth <= 768
  }
  window.addEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.tunnel-list {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h2 {
      margin: 0;
      color: var(--n-text-color);
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .tunnel-list {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      
      h2 {
        text-align: center;
      }
    }
  }
}
</style>
