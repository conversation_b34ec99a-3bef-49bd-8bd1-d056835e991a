<template>
  <div class="dashboard-home">
    <!-- 欢迎横幅 -->
    <n-card class="welcome-banner" :bordered="false">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="greeting">{{ greeting }} {{ greetingEmoji }}</h1>
          <p class="welcome-message">欢迎回来，{{ username }}！</p>
          <p class="current-time">{{ currentTime }}</p>
        </div>
        <div class="sign-in-section">
          <n-button
            v-if="!todaySignedIn"
            type="primary"
            size="large"
            @click="handleSignIn"
            :loading="signingIn"
          >
            签到
          </n-button>
          <div v-else class="signed-in">
            <n-icon size="24" color="#52c41a">
              <CheckCircleOutline />
            </n-icon>
            <span>今日已签到</span>
          </div>
          
          <!-- 签到信息提示 -->
          <n-popover
            v-if="!isMobile"
            trigger="hover"
            placement="bottom"
          >
            <template #trigger>
              <n-button text class="sign-info-btn">
                <n-icon size="16">
                  <InformationCircleOutline />
                </n-icon>
                签到信息
              </n-button>
            </template>
            <div class="sign-info">
              <p><strong>上次签到时间：</strong>{{ lastSignTime || '暂无' }}</p>
              <p><strong>累计签到积分：</strong>{{ totalSignPoints }}</p>
              <p><strong>累计签到次数：</strong>{{ totalSignCount }}</p>
              <p><strong>今日签到人数：</strong>{{ todaySignCount }}</p>
            </div>
          </n-popover>
          
          <!-- 移动端点击显示签到信息 -->
          <n-button
            v-if="isMobile"
            text
            class="sign-info-btn"
            @click="showSignInfo = true"
          >
            <n-icon size="16">
              <InformationCircleOutline />
            </n-icon>
            签到信息
          </n-button>
        </div>
      </div>
    </n-card>

    <n-grid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16" class="content-grid">
      <!-- 用户信息卡片 -->
      <n-grid-item>
        <n-card title="用户信息" :bordered="false">
          <div class="user-info">
            <div class="info-row">
              <span class="label">用户名</span>
              <span class="value">{{ userInfo.username }}</span>
            </div>
            <div class="info-row">
              <span class="label">用户 ID</span>
              <span class="value">{{ userInfo.userId }}</span>
            </div>
            <div class="info-row">
              <span class="label">实名认证</span>
              <n-tag :type="userInfo.verified ? 'success' : 'warning'">
                {{ userInfo.verified ? '已认证' : '未认证' }}
              </n-tag>
            </div>
            <div class="info-row">
              <span class="label">用户组</span>
              <n-tag type="info">{{ userInfo.userGroup }}</n-tag>
            </div>
            <div class="info-row">
              <span class="label">注册时间</span>
              <span class="value">{{ userInfo.registerTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">注册邮箱</span>
              <span class="value">{{ userInfo.email }}</span>
            </div>
            <div class="info-row">
              <span class="label">隧道数量</span>
              <span class="value">{{ userInfo.tunnelCount }}</span>
            </div>
            <div class="info-row">
              <span class="label">剩余积分</span>
              <span class="value">{{ userInfo.points }}</span>
            </div>
            <div class="info-row">
              <span class="label">入站带宽</span>
              <span class="value">{{ userInfo.inboundBandwidth }}</span>
            </div>
            <div class="info-row">
              <span class="label">出站带宽</span>
              <span class="value">{{ userInfo.outboundBandwidth }}</span>
            </div>
          </div>
        </n-card>
      </n-grid-item>

      <!-- 系统公告卡片 -->
      <n-grid-item>
        <n-card title="系统公告" :bordered="false">
          <div class="announcement-content">
            <div v-if="announcements.length === 0" class="no-announcement">
              暂无公告
            </div>
            <div v-else>
              <div
                v-for="(announcement, index) in displayedAnnouncements"
                :key="index"
                class="announcement-item"
              >
                <div class="announcement-title">{{ announcement.title }}</div>
                <div class="announcement-time">{{ announcement.time }}</div>
                <div class="announcement-text">{{ announcement.content }}</div>
              </div>
              
              <n-button
                v-if="announcements.length > 10"
                text
                type="primary"
                @click="toggleAnnouncements"
                class="toggle-btn"
              >
                {{ showAllAnnouncements ? '收起' : '展开更多' }}
              </n-button>
            </div>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 官方广告卡片 -->
    <n-card class="ad-banner" :bordered="false" v-if="advertisement.show">
      <div class="ad-content" @click="handleAdClick">
        <img :src="advertisement.image" :alt="advertisement.title" class="ad-image" />
        <div class="ad-text">
          <h3>{{ advertisement.title }}</h3>
          <p>{{ advertisement.description }}</p>
        </div>
      </div>
    </n-card>

    <!-- 移动端签到信息弹窗 -->
    <n-modal v-model:show="showSignInfo" preset="card" title="签到信息" style="width: 90%; max-width: 400px;">
      <div class="sign-info">
        <p><strong>上次签到时间：</strong>{{ lastSignTime || '暂无' }}</p>
        <p><strong>累计签到积分：</strong>{{ totalSignPoints }}</p>
        <p><strong>累计签到次数：</strong>{{ totalSignCount }}</p>
        <p><strong>今日签到人数：</strong>{{ todaySignCount }}</p>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { CheckCircleOutline, InformationCircleOutline } from '@vicons/ionicons5'

const message = useMessage()

// 响应式检测
const isMobile = ref(window.innerWidth <= 768)

// 用户信息
const username = ref(localStorage.getItem('username') || '用户')
const userInfo = ref({
  username: localStorage.getItem('username') || '用户',
  userId: '12345',
  verified: false,
  userGroup: localStorage.getItem('group') || '普通用户',
  registerTime: '2024-01-01',
  email: '<EMAIL>',
  tunnelCount: 0,
  points: 100,
  inboundBandwidth: '10 Mbps',
  outboundBandwidth: '10 Mbps'
})

// 时间问候
const currentTime = ref('')
const greeting = ref('')
const greetingEmoji = ref('')

// 签到相关
const todaySignedIn = ref(false)
const signingIn = ref(false)
const showSignInfo = ref(false)
const lastSignTime = ref('')
const totalSignPoints = ref(0)
const totalSignCount = ref(0)
const todaySignCount = ref(0)

// 公告相关
const announcements = ref([])
const showAllAnnouncements = ref(false)
const displayedAnnouncements = computed(() => {
  return showAllAnnouncements.value ? announcements.value : announcements.value.slice(0, 10)
})

// 广告相关
const advertisement = ref({
  show: true,
  title: '官方广告位',
  description: '这里是广告内容描述',
  image: '/api/placeholder/800/200',
  link: '#'
})

// 问候语和emoji
const greetingEmojis = ['🌞', '🌈', '😃', '✨', '🥳', '🎉', '🦄', '🍀', '😺', '🚀', '🌸', '🍉', '🧸', '🎈', '😎']

// 更新时间和问候语
const updateTimeAndGreeting = () => {
  const now = new Date()
  const hour = now.getHours()
  
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  
  if (hour >= 0 && hour < 6) {
    greeting.value = '凌晨好'
  } else if (hour >= 6 && hour < 12) {
    greeting.value = '早上好'
  } else if (hour >= 12 && hour < 18) {
    greeting.value = '下午好'
  } else {
    greeting.value = '晚上好'
  }
  
  // 随机选择emoji
  greetingEmoji.value = greetingEmojis[Math.floor(Math.random() * greetingEmojis.length)]
}

// 签到处理
const handleSignIn = async () => {
  signingIn.value = true
  try {
    // 这里调用签到API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    todaySignedIn.value = true
    totalSignPoints.value += 10
    totalSignCount.value += 1
    todaySignCount.value += 1
    lastSignTime.value = new Date().toLocaleString('zh-CN')
    message.success('签到成功！获得10积分')
  } catch (error) {
    message.error('签到失败，请重试')
  } finally {
    signingIn.value = false
  }
}

// 切换公告显示
const toggleAnnouncements = () => {
  showAllAnnouncements.value = !showAllAnnouncements.value
}

// 广告点击处理
const handleAdClick = () => {
  if (advertisement.value.link && advertisement.value.link !== '#') {
    window.open(advertisement.value.link, '_blank')
  }
}

onMounted(() => {
  updateTimeAndGreeting()
  // 每秒更新时间
  setInterval(updateTimeAndGreeting, 1000)
  
  // 监听窗口大小变化
  const handleResize = () => {
    isMobile.value = window.innerWidth <= 768
  }
  window.addEventListener('resize', handleResize)
  
  // 加载用户数据和公告数据
  // loadUserData()
  // loadAnnouncements()
})
</script>

<style lang="scss" scoped>
.dashboard-home {
  padding: 0;
  
  .welcome-banner {
    margin-bottom: 16px;
    
    .banner-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      
      .welcome-text {
        flex: 1;
        
        .greeting {
          font-size: 24px;
          font-weight: bold;
          margin: 0 0 8px 0;
          color: var(--n-text-color);
        }
        
        .welcome-message {
          font-size: 16px;
          margin: 0 0 4px 0;
          color: var(--n-text-color-2);
        }
        
        .current-time {
          font-size: 14px;
          margin: 0;
          color: var(--n-text-color-3);
        }
      }
      
      .sign-in-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        
        .signed-in {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #52c41a;
          font-weight: 500;
        }
        
        .sign-info-btn {
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }
  }
  
  .content-grid {
    margin-bottom: 16px;
  }
  
  .user-info {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--n-border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .label {
        font-weight: 500;
        color: var(--n-text-color-2);
      }
      
      .value {
        color: var(--n-text-color);
      }
    }
  }
  
  .announcement-content {
    .no-announcement {
      text-align: center;
      color: var(--n-text-color-3);
      padding: 20px;
    }
    
    .announcement-item {
      padding: 12px 0;
      border-bottom: 1px solid var(--n-border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .announcement-title {
        font-weight: 500;
        margin-bottom: 4px;
        color: var(--n-text-color);
      }
      
      .announcement-time {
        font-size: 12px;
        color: var(--n-text-color-3);
        margin-bottom: 8px;
      }
      
      .announcement-text {
        color: var(--n-text-color-2);
        line-height: 1.5;
      }
    }
    
    .toggle-btn {
      width: 100%;
      margin-top: 12px;
    }
  }
  
  .ad-banner {
    .ad-content {
      display: flex;
      align-items: center;
      gap: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
      }
      
      .ad-image {
        width: 120px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
      }
      
      .ad-text {
        flex: 1;
        
        h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          color: var(--n-text-color);
        }
        
        p {
          margin: 0;
          color: var(--n-text-color-2);
        }
      }
    }
  }
  
  .sign-info {
    p {
      margin: 8px 0;
      
      strong {
        color: var(--n-text-color);
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .dashboard-home {
    .welcome-banner .banner-content {
      flex-direction: column;
      text-align: center;
      
      .welcome-text {
        text-align: center;
      }
    }
    
    .ad-banner .ad-content {
      flex-direction: column;
      text-align: center;
      
      .ad-image {
        width: 100%;
        height: 120px;
      }
    }
  }
}
</style>
