<template>
  <div class="left-menu">
    <n-menu
      :collapsed-width="64"
      :collapsed-icon-size="22"
      :options="menuOptions"
      :value="activeKey"
      @update:value="handleMenuSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NIcon, type MenuOption } from 'naive-ui'
import {
  HomeOutline,
  PersonOutline,
  ServerOutline,
  InformationCircleOutline,
  SettingsOutline,
  PeopleOutline,
  ListOutline,
  CloudOutline,
  DocumentTextOutline,
  StatsChartOutline,
  DownloadOutline,
  AddCircleOutline,
  EyeOutline
} from '@vicons/ionicons5'

const router = useRouter()
const route = useRoute()

// 当前激活的菜单项
const activeKey = computed(() => route.name as string)

// 渲染图标的辅助函数
function renderIcon(icon: any) {
  return () => h(NIcon, null, { default: () => h(icon) })
}

// 检查用户是否为管理员
const isAdmin = computed(() => {
  const userGroup = localStorage.getItem('group') || ''
  return userGroup === 'admin' || userGroup === 'administrator'
})

// 菜单选项
const menuOptions = computed((): MenuOption[] => {
  const baseOptions: MenuOption[] = [
    {
      label: '首页',
      key: 'dashboard-home',
      icon: renderIcon(HomeOutline)
    },
    {
      label: '个人资料',
      key: 'profile',
      icon: renderIcon(PersonOutline)
    },
    {
      label: '隧道相关',
      key: 'tunnel',
      icon: renderIcon(ServerOutline),
      children: [
        {
          label: '隧道列表',
          key: 'tunnel-list',
          icon: renderIcon(ListOutline)
        },
        {
          label: '创建隧道',
          key: 'tunnel-create',
          icon: renderIcon(AddCircleOutline)
        },
        {
          label: '节点状态',
          key: 'node-status',
          icon: renderIcon(EyeOutline)
        },
        {
          label: '客户端下载',
          key: 'client-download',
          icon: renderIcon(DownloadOutline)
        }
      ]
    },
    {
      label: '其他信息',
      key: 'other',
      icon: renderIcon(InformationCircleOutline),
      children: [
        {
          label: '关于面板',
          key: 'about',
          icon: renderIcon(DocumentTextOutline)
        }
      ]
    }
  ]

  // 如果是管理员，添加管理菜单
  if (isAdmin.value) {
    baseOptions.push({
      label: '管理',
      key: 'admin',
      icon: renderIcon(SettingsOutline),
      children: [
        {
          label: '用户管理',
          key: 'admin-users',
          icon: renderIcon(PeopleOutline)
        },
        {
          label: '隧道管理',
          key: 'admin-tunnels',
          icon: renderIcon(ServerOutline)
        },
        {
          label: '节点管理',
          key: 'admin-nodes',
          icon: renderIcon(CloudOutline)
        },
        {
          label: '操作日志',
          key: 'admin-logs',
          icon: renderIcon(StatsChartOutline)
        },
        {
          label: '系统设置',
          key: 'admin-settings',
          icon: renderIcon(SettingsOutline)
        }
      ]
    })
  }

  return baseOptions
})

// 处理菜单选择
const handleMenuSelect = (key: string) => {
  router.push({ name: key })
}
</script>

<style lang="scss" scoped>
.left-menu {
  height: 100%;
  
  :deep(.n-menu) {
    .n-menu-item-content {
      padding-left: 18px !important;
    }
    
    .n-menu-item-content--collapsed {
      padding-left: 18px !important;
    }
    
    .n-submenu-children .n-menu-item-content {
      padding-left: 42px !important;
    }
  }
}
</style>
