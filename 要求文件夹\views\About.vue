<template>
  <div class="about-page">
    <div class="page-header">
      <h2>关于面板</h2>
    </div>

    <!-- 系统信息 -->
    <n-card :bordered="false" title="系统信息" class="system-info">
      <n-grid :cols="isMobile ? 1 : 2" :x-gap="24" :y-gap="16">
        <n-grid-item>
          <div class="info-section">
            <h4>面板信息</h4>
            <div class="info-list">
              <div class="info-item">
                <span class="label">面板名称</span>
                <span class="value">XunFrp 管理面板</span>
              </div>
              <div class="info-item">
                <span class="label">面板版本</span>
                <span class="value">{{ panelVersion }}</span>
              </div>
              <div class="info-item">
                <span class="label">构建时间</span>
                <span class="value">{{ buildTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">运行时间</span>
                <span class="value">{{ uptime }}</span>
              </div>
            </div>
          </div>
        </n-grid-item>

        <n-grid-item>
          <div class="info-section">
            <h4>服务器信息</h4>
            <div class="info-list">
              <div class="info-item">
                <span class="label">服务器时间</span>
                <span class="value">{{ serverTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">时区</span>
                <span class="value">{{ timezone }}</span>
              </div>
              <div class="info-item">
                <span class="label">在线用户</span>
                <span class="value">{{ onlineUsers }}</span>
              </div>
              <div class="info-item">
                <span class="label">活跃隧道</span>
                <span class="value">{{ activeTunnels }}</span>
              </div>
            </div>
          </div>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 技术栈 -->
    <n-card :bordered="false" title="技术栈" class="tech-stack">
      <n-grid :cols="isMobile ? 1 : 2" :x-gap="24" :y-gap="16">
        <n-grid-item>
          <div class="tech-section">
            <h4>前端技术</h4>
            <div class="tech-list">
              <div
                v-for="tech in frontendTech"
                :key="tech.name"
                class="tech-item"
              >
                <div class="tech-info">
                  <n-icon size="20" :color="tech.color">
                    <component :is="tech.icon" />
                  </n-icon>
                  <div class="tech-details">
                    <span class="tech-name">{{ tech.name }}</span>
                    <span class="tech-version">{{ tech.version }}</span>
                  </div>
                </div>
                <div class="tech-description">{{ tech.description }}</div>
              </div>
            </div>
          </div>
        </n-grid-item>

        <n-grid-item>
          <div class="tech-section">
            <h4>后端技术</h4>
            <div class="tech-list">
              <div
                v-for="tech in backendTech"
                :key="tech.name"
                class="tech-item"
              >
                <div class="tech-info">
                  <n-icon size="20" :color="tech.color">
                    <component :is="tech.icon" />
                  </n-icon>
                  <div class="tech-details">
                    <span class="tech-name">{{ tech.name }}</span>
                    <span class="tech-version">{{ tech.version }}</span>
                  </div>
                </div>
                <div class="tech-description">{{ tech.description }}</div>
              </div>
            </div>
          </div>
        </n-grid-item>
      </n-grid>
    </n-card>

    <!-- 开源信息 -->
    <n-card :bordered="false" title="开源信息" class="open-source">
      <div class="license-info">
        <n-space align="center" :size="16">
          <n-icon size="32" color="#18a058">
            <CodeSlashOutline />
          </n-icon>
          <div>
            <h4>开源协议</h4>
            <p>本项目基于 MIT 协议开源，您可以自由使用、修改和分发。</p>
          </div>
        </n-space>
      </div>

      <n-divider />

      <div class="project-links">
        <h4>项目链接</h4>
        <n-space :size="16">
          <n-button
            tag="a"
            :href="projectInfo.github"
            target="_blank"
            type="primary"
            ghost
          >
            <n-icon size="16">
              <LogoGithub />
            </n-icon>
            GitHub
          </n-button>
          <n-button
            tag="a"
            :href="projectInfo.docs"
            target="_blank"
            type="info"
            ghost
          >
            <n-icon size="16">
              <DocumentTextOutline />
            </n-icon>
            文档
          </n-button>
          <n-button
            tag="a"
            :href="projectInfo.issues"
            target="_blank"
            type="warning"
            ghost
          >
            <n-icon size="16">
              <BugReportOutline />
            </n-icon>
            问题反馈
          </n-button>
        </n-space>
      </div>
    </n-card>

    <!-- 致谢 -->
    <n-card :bordered="false" title="特别致谢" class="acknowledgments">
      <div class="thanks-content">
        <p>感谢以下开源项目和贡献者：</p>
        <ul class="thanks-list">
          <li><strong>FRP</strong> - 高性能的内网穿透工具</li>
          <li><strong>Vue.js</strong> - 渐进式 JavaScript 框架</li>
          <li><strong>Naive UI</strong> - Vue 3 组件库</li>
          <li><strong>TypeScript</strong> - JavaScript 的超集</li>
          <li><strong>Vite</strong> - 下一代前端构建工具</li>
          <li><strong>Node.js</strong> - JavaScript 运行时环境</li>
        </ul>
      </div>
    </n-card>

    <!-- 版权信息 -->
    <n-card :bordered="false" class="copyright">
      <div class="copyright-content">
        <n-space justify="center" align="center" :size="8">
          <n-icon size="20" color="#666">
            <HeartOutline />
          </n-icon>
          <span>© {{ currentYear }} XunFrp. Made with love by the community.</span>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  CodeSlashOutline,
  LogoGithub,
  DocumentTextOutline,
  BugReportOutline,
  HeartOutline,
  LogoVue,
  LogoNodejs,
  ServerOutline,
  DatabaseOutline,
  CloudOutline
} from '@vicons/ionicons5'

// 响应式检测
const isMobile = ref(window.innerWidth <= 768)

// 系统信息
const panelVersion = ref('v1.0.0')
const buildTime = ref('2024-01-15 10:30:00')
const serverTime = ref('')
const timezone = ref('Asia/Shanghai')
const onlineUsers = ref(156)
const activeTunnels = ref(342)
const uptime = ref('')

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 项目信息
const projectInfo = {
  github: 'https://github.com/example/xunfrp',
  docs: 'https://docs.xunfrp.com',
  issues: 'https://github.com/example/xunfrp/issues'
}

// 前端技术栈
const frontendTech = ref([
  {
    name: 'Vue.js',
    version: 'v3.4.0',
    description: '渐进式 JavaScript 框架',
    icon: LogoVue,
    color: '#4fc08d'
  },
  {
    name: 'TypeScript',
    version: 'v5.0.0',
    description: 'JavaScript 的超集',
    icon: CodeSlashOutline,
    color: '#3178c6'
  },
  {
    name: 'Naive UI',
    version: 'v2.38.0',
    description: 'Vue 3 组件库',
    icon: CloudOutline,
    color: '#36ad6a'
  },
  {
    name: 'Vite',
    version: 'v5.0.0',
    description: '下一代前端构建工具',
    icon: ServerOutline,
    color: '#646cff'
  }
])

// 后端技术栈
const backendTech = ref([
  {
    name: 'Node.js',
    version: 'v20.0.0',
    description: 'JavaScript 运行时环境',
    icon: LogoNodejs,
    color: '#339933'
  },
  {
    name: 'Express',
    version: 'v4.18.0',
    description: 'Web 应用框架',
    icon: ServerOutline,
    color: '#000000'
  },
  {
    name: 'MySQL',
    version: 'v8.0.0',
    description: '关系型数据库',
    icon: DatabaseOutline,
    color: '#4479a1'
  },
  {
    name: 'Redis',
    version: 'v7.0.0',
    description: '内存数据库',
    icon: DatabaseOutline,
    color: '#dc382d'
  }
])

// 更新服务器时间
const updateServerTime = () => {
  serverTime.value = new Date().toLocaleString('zh-CN')
}

// 计算运行时间
const calculateUptime = () => {
  const startTime = new Date('2024-01-01T00:00:00')
  const now = new Date()
  const diff = now.getTime() - startTime.getTime()
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  uptime.value = `${days} 天 ${hours} 小时 ${minutes} 分钟`
}

onMounted(() => {
  // 初始化时间
  updateServerTime()
  calculateUptime()
  
  // 定时更新时间
  const timeInterval = setInterval(() => {
    updateServerTime()
    calculateUptime()
  }, 1000)
  
  // 监听窗口大小变化
  const handleResize = () => {
    isMobile.value = window.innerWidth <= 768
  }
  window.addEventListener('resize', handleResize)
  
  // 清理定时器
  onUnmounted(() => {
    clearInterval(timeInterval)
    window.removeEventListener('resize', handleResize)
  })
})
</script>

<style lang="scss" scoped>
.about-page {
  .page-header {
    margin-bottom: 16px;
    
    h2 {
      margin: 0;
      color: var(--n-text-color);
    }
  }
  
  .system-info,
  .tech-stack,
  .open-source,
  .acknowledgments {
    margin-bottom: 16px;
  }
  
  .info-section,
  .tech-section {
    h4 {
      margin: 0 0 16px 0;
      color: var(--n-text-color);
      font-size: 16px;
    }
  }
  
  .info-list {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--n-border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .label {
        color: var(--n-text-color-2);
        font-size: 14px;
      }
      
      .value {
        color: var(--n-text-color);
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
  
  .tech-list {
    .tech-item {
      padding: 12px 0;
      border-bottom: 1px solid var(--n-border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .tech-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 4px;
        
        .tech-details {
          display: flex;
          flex-direction: column;
          
          .tech-name {
            font-weight: 500;
            color: var(--n-text-color);
          }
          
          .tech-version {
            font-size: 12px;
            color: var(--n-text-color-3);
          }
        }
      }
      
      .tech-description {
        font-size: 14px;
        color: var(--n-text-color-2);
        margin-left: 32px;
      }
    }
  }
  
  .license-info {
    h4 {
      margin: 0 0 8px 0;
      color: var(--n-text-color);
    }
    
    p {
      margin: 0;
      color: var(--n-text-color-2);
    }
  }
  
  .project-links {
    h4 {
      margin: 0 0 16px 0;
      color: var(--n-text-color);
    }
  }
  
  .thanks-content {
    p {
      margin: 0 0 16px 0;
      color: var(--n-text-color);
    }
    
    .thanks-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        color: var(--n-text-color-2);
        
        strong {
          color: var(--n-text-color);
        }
      }
    }
  }
  
  .copyright {
    .copyright-content {
      text-align: center;
      color: var(--n-text-color-3);
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .about-page {
    .tech-item .tech-description {
      margin-left: 0;
      margin-top: 8px;
    }
  }
}
</style>
