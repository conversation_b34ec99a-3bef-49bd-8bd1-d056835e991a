<template>
  <div class="client-download">
    <div class="page-header">
      <h2>客户端下载</h2>
      <n-button @click="checkUpdates" :loading="checking">
        <n-icon size="16">
          <RefreshOutline />
        </n-icon>
        检查更新
      </n-button>
    </div>

    <!-- 版本信息 -->
    <n-card :bordered="false" class="version-info">
      <n-space align="center">
        <n-icon size="32" color="#18a058">
          <DownloadOutline />
        </n-icon>
        <div>
          <h3>FRP 客户端</h3>
          <p>当前最新版本：<n-tag type="success">{{ latestVersion }}</n-tag></p>
          <p class="version-desc">支持 Windows、Linux、macOS 等多个平台</p>
        </div>
      </n-space>
    </n-card>

    <!-- 下载列表 -->
    <n-card :bordered="false" title="下载列表">
      <div class="download-grid">
        <div
          v-for="client in clients"
          :key="client.id"
          class="download-card"
        >
          <div class="client-header">
            <div class="client-info">
              <n-icon size="32" :color="client.color">
                <component :is="client.icon" />
              </n-icon>
              <div class="client-details">
                <h4>{{ client.name }}</h4>
                <p>{{ client.description }}</p>
              </div>
            </div>
          </div>

          <div class="client-versions">
            <div
              v-for="version in client.versions"
              :key="version.arch"
              class="version-item"
            >
              <div class="version-info">
                <span class="arch">{{ version.arch }}</span>
                <span class="size">{{ version.size }}</span>
              </div>
              <n-button
                type="primary"
                size="small"
                @click="downloadClient(version)"
                :loading="version.downloading"
              >
                <n-icon size="14">
                  <DownloadOutline />
                </n-icon>
                下载
              </n-button>
            </div>
          </div>

          <div class="client-footer">
            <n-space size="small">
              <n-button
                text
                size="small"
                @click="showChangelog(client)"
              >
                更新日志
              </n-button>
              <n-button
                text
                size="small"
                @click="showInstallGuide(client)"
              >
                安装指南
              </n-button>
            </n-space>
          </div>
        </div>
      </div>
    </n-card>

    <!-- 使用说明 -->
    <n-card :bordered="false" title="使用说明">
      <n-steps :current="1" :status="'process'">
        <n-step title="下载客户端">
          <div class="step-content">
            根据您的操作系统选择对应的客户端版本进行下载
          </div>
        </n-step>
        <n-step title="创建配置">
          <div class="step-content">
            在隧道管理页面创建隧道并获取配置文件
          </div>
        </n-step>
        <n-step title="启动客户端">
          <div class="step-content">
            使用配置文件启动客户端：<n-code inline>frpc -c frpc.ini</n-code>
          </div>
        </n-step>
        <n-step title="开始使用">
          <div class="step-content">
            客户端连接成功后即可通过外网访问您的内网服务
          </div>
        </n-step>
      </n-steps>
    </n-card>

    <!-- 常见问题 -->
    <n-card :bordered="false" title="常见问题">
      <n-collapse>
        <n-collapse-item
          v-for="(faq, index) in faqs"
          :key="index"
          :title="faq.question"
          :name="index"
        >
          <div v-html="faq.answer"></div>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <!-- 更新日志弹窗 -->
    <n-modal v-model:show="showChangelogModal" preset="card" title="更新日志" style="width: 600px;">
      <div class="changelog-content">
        <div v-for="log in changelog" :key="log.version" class="changelog-item">
          <h4>{{ log.version }} <n-tag size="small">{{ log.date }}</n-tag></h4>
          <ul>
            <li v-for="change in log.changes" :key="change">{{ change }}</li>
          </ul>
        </div>
      </div>
    </n-modal>

    <!-- 安装指南弹窗 -->
    <n-modal v-model:show="showGuideModal" preset="card" :title="`${selectedClient?.name} 安装指南`" style="width: 700px;">
      <div class="guide-content" v-if="selectedClient">
        <n-steps vertical>
          <n-step
            v-for="(step, index) in selectedClient.installSteps"
            :key="index"
            :title="step.title"
          >
            <div class="guide-step">
              <p>{{ step.description }}</p>
              <n-code v-if="step.command" :code="step.command" language="bash" />
            </div>
          </n-step>
        </n-steps>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  RefreshOutline,
  DownloadOutline,
  DesktopOutline,
  PhonePortraitOutline,
  TerminalOutline
} from '@vicons/ionicons5'

const message = useMessage()

// 数据状态
const checking = ref(false)
const latestVersion = ref('v0.52.3')
const showChangelogModal = ref(false)
const showGuideModal = ref(false)
const selectedClient = ref(null)

// 客户端列表
const clients = ref([
  {
    id: 'windows',
    name: 'Windows',
    description: 'Windows 7/8/10/11 (32位/64位)',
    icon: DesktopOutline,
    color: '#0078d4',
    versions: [
      {
        arch: 'Windows x64',
        size: '8.2 MB',
        url: '/downloads/frpc_windows_amd64.exe',
        downloading: false
      },
      {
        arch: 'Windows x86',
        size: '7.8 MB',
        url: '/downloads/frpc_windows_386.exe',
        downloading: false
      }
    ],
    installSteps: [
      {
        title: '下载客户端',
        description: '选择对应架构的客户端程序下载到本地'
      },
      {
        title: '创建配置文件',
        description: '在客户端同目录下创建 frpc.ini 配置文件',
        command: `[common]
server_addr = frp.example.com
server_port = 7000
token = your_token

[web]
type = http
local_ip = 127.0.0.1
local_port = 8080
remote_port = 80`
      },
      {
        title: '启动客户端',
        description: '在命令行中运行客户端程序',
        command: 'frpc.exe -c frpc.ini'
      }
    ]
  },
  {
    id: 'linux',
    name: 'Linux',
    description: 'Linux (x64/ARM/ARM64)',
    icon: TerminalOutline,
    color: '#f57c00',
    versions: [
      {
        arch: 'Linux x64',
        size: '7.9 MB',
        url: '/downloads/frpc_linux_amd64',
        downloading: false
      },
      {
        arch: 'Linux ARM',
        size: '7.2 MB',
        url: '/downloads/frpc_linux_arm',
        downloading: false
      },
      {
        arch: 'Linux ARM64',
        size: '7.5 MB',
        url: '/downloads/frpc_linux_arm64',
        downloading: false
      }
    ],
    installSteps: [
      {
        title: '下载客户端',
        description: '使用 wget 或 curl 下载客户端程序',
        command: 'wget https://example.com/downloads/frpc_linux_amd64'
      },
      {
        title: '添加执行权限',
        description: '为客户端程序添加可执行权限',
        command: 'chmod +x frpc_linux_amd64'
      },
      {
        title: '创建配置文件',
        description: '创建配置文件 frpc.ini',
        command: 'nano frpc.ini'
      },
      {
        title: '启动客户端',
        description: '运行客户端程序',
        command: './frpc_linux_amd64 -c frpc.ini'
      }
    ]
  },
  {
    id: 'macos',
    name: 'macOS',
    description: 'macOS (Intel/Apple Silicon)',
    icon: DesktopOutline,
    color: '#007aff',
    versions: [
      {
        arch: 'macOS Intel',
        size: '8.1 MB',
        url: '/downloads/frpc_darwin_amd64',
        downloading: false
      },
      {
        arch: 'macOS Apple Silicon',
        size: '7.8 MB',
        url: '/downloads/frpc_darwin_arm64',
        downloading: false
      }
    ],
    installSteps: [
      {
        title: '下载客户端',
        description: '下载对应架构的客户端程序'
      },
      {
        title: '添加执行权限',
        description: '在终端中为程序添加执行权限',
        command: 'chmod +x frpc_darwin_amd64'
      },
      {
        title: '创建配置文件',
        description: '创建 frpc.ini 配置文件'
      },
      {
        title: '启动客户端',
        description: '运行客户端程序',
        command: './frpc_darwin_amd64 -c frpc.ini'
      }
    ]
  }
])

// 更新日志
const changelog = ref([
  {
    version: 'v0.52.3',
    date: '2024-01-15',
    changes: [
      '修复了连接稳定性问题',
      '优化了内存使用',
      '增加了新的配置选项',
      '修复了已知的安全漏洞'
    ]
  },
  {
    version: 'v0.52.2',
    date: '2024-01-01',
    changes: [
      '支持新的协议类型',
      '改进了错误处理',
      '优化了性能表现'
    ]
  }
])

// 常见问题
const faqs = ref([
  {
    question: '如何选择合适的客户端版本？',
    answer: '请根据您的操作系统和架构选择对应版本。Windows 用户通常选择 x64 版本，Linux 用户需要根据服务器架构选择。'
  },
  {
    question: '客户端无法连接到服务器怎么办？',
    answer: '请检查：<br/>1. 网络连接是否正常<br/>2. 配置文件中的服务器地址和端口是否正确<br/>3. Token 是否有效<br/>4. 防火墙是否阻止了连接'
  },
  {
    question: '如何设置开机自启动？',
    answer: 'Windows: 可以将客户端添加到启动文件夹或使用任务计划程序<br/>Linux: 可以创建 systemd 服务或添加到 rc.local<br/>macOS: 可以使用 launchd 创建启动项'
  },
  {
    question: '客户端占用资源过多怎么办？',
    answer: '可以通过配置文件调整以下参数：<br/>- max_pool_count: 连接池大小<br/>- heartbeat_interval: 心跳间隔<br/>- heartbeat_timeout: 心跳超时'
  }
])

// 检查更新
const checkUpdates = async () => {
  checking.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('已是最新版本')
  } catch (error) {
    message.error('检查更新失败')
  } finally {
    checking.value = false
  }
}

// 下载客户端
const downloadClient = async (version: any) => {
  version.downloading = true
  try {
    // 模拟下载
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 创建下载链接
    const link = document.createElement('a')
    link.href = version.url
    link.download = version.url.split('/').pop()
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    message.success(`${version.arch} 下载完成`)
  } catch (error) {
    message.error('下载失败')
  } finally {
    version.downloading = false
  }
}

// 显示更新日志
const showChangelog = (client: any) => {
  showChangelogModal.value = true
}

// 显示安装指南
const showInstallGuide = (client: any) => {
  selectedClient.value = client
  showGuideModal.value = true
}

onMounted(() => {
  // 页面加载时可以检查最新版本
})
</script>

<style lang="scss" scoped>
.client-download {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h2 {
      margin: 0;
      color: var(--n-text-color);
    }
  }
  
  .version-info {
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      color: var(--n-text-color);
    }
    
    p {
      margin: 4px 0;
      color: var(--n-text-color-2);
      
      &.version-desc {
        font-size: 14px;
        color: var(--n-text-color-3);
      }
    }
  }
  
  .download-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 16px;
  }
  
  .download-card {
    border: 1px solid var(--n-border-color);
    border-radius: 6px;
    padding: 16px;
    background: var(--n-card-color);
    
    .client-header {
      margin-bottom: 16px;
      
      .client-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .client-details {
          h4 {
            margin: 0 0 4px 0;
            font-size: 16px;
            color: var(--n-text-color);
          }
          
          p {
            margin: 0;
            font-size: 14px;
            color: var(--n-text-color-2);
          }
        }
      }
    }
    
    .client-versions {
      margin-bottom: 16px;
      
      .version-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--n-border-color);
        
        &:last-child {
          border-bottom: none;
        }
        
        .version-info {
          .arch {
            font-weight: 500;
            color: var(--n-text-color);
          }
          
          .size {
            margin-left: 8px;
            font-size: 12px;
            color: var(--n-text-color-3);
          }
        }
      }
    }
    
    .client-footer {
      padding-top: 12px;
      border-top: 1px solid var(--n-border-color);
    }
  }
  
  .step-content {
    color: var(--n-text-color-2);
    font-size: 14px;
  }
  
  .changelog-content {
    .changelog-item {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        color: var(--n-text-color);
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin: 4px 0;
          color: var(--n-text-color-2);
        }
      }
    }
  }
  
  .guide-content {
    .guide-step {
      p {
        margin: 0 0 8px 0;
        color: var(--n-text-color-2);
      }
    }
  }
}

@media (max-width: 768px) {
  .client-download {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      
      h2 {
        text-align: center;
      }
    }
    
    .download-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
