<template>
  <div class="profile-page">
    <n-grid :cols="isMobile ? 1 : 2" :x-gap="16" :y-gap="16">
      <!-- 账户设置卡片 -->
      <n-grid-item>
        <n-card title="账户设置" :bordered="false">
          <n-space vertical :size="16">
            <!-- 重置访问密钥 -->
            <div class="setting-item">
              <div class="setting-header">
                <h4>重置访问密钥</h4>
                <p class="setting-desc">此操作不可逆，重置后所有客户端均需重新登录</p>
              </div>
              <n-button type="error" @click="showResetKeyModal = true">
                重置访问密钥
              </n-button>
            </div>

            <!-- 修改用户名 -->
            <div class="setting-item">
              <div class="setting-header">
                <h4>修改用户名</h4>
                <p class="setting-desc">点击这里可以修改您的用户名</p>
              </div>
              <n-button @click="showUsernameModal = true">
                修改用户名
              </n-button>
            </div>

            <!-- 更改头像 -->
            <div class="setting-item">
              <div class="setting-header">
                <h4>更改头像</h4>
                <p class="setting-desc">不支持上传图片文件，只要图片链接</p>
              </div>
              <n-button @click="showAvatarModal = true">
                更改头像
              </n-button>
            </div>

            <!-- 修改密码 -->
            <div class="setting-item">
              <div class="setting-header">
                <h4>修改密码</h4>
                <p class="setting-desc">点击这里可以修改您的登录密码</p>
              </div>
              <n-button @click="showPasswordModal = true">
                修改密码
              </n-button>
            </div>

            <!-- 更改邮箱 -->
            <div class="setting-item">
              <div class="setting-header">
                <h4>更改邮箱</h4>
                <p class="setting-desc">此操作风险较大，请谨慎操作</p>
              </div>
              <n-button type="warning" @click="showEmailModal = true">
                更改邮箱
              </n-button>
            </div>
          </n-space>
        </n-card>
      </n-grid-item>

      <!-- 账户详情卡片 -->
      <n-grid-item>
        <n-card :bordered="false">
          <template #header>
            <div class="card-header">
              <span>账户详情</span>
              <n-button
                text
                type="error"
                @click="showDeleteAccountModal = true"
                class="delete-btn"
              >
                <n-icon size="16">
                  <CloseOutline />
                </n-icon>
              </n-button>
            </div>
          </template>
          
          <div class="account-details">
            <div class="detail-row">
              <span class="label">用户名</span>
              <span class="value">{{ userInfo.username }}</span>
            </div>
            <div class="detail-row">
              <span class="label">用户 ID</span>
              <span class="value">{{ userInfo.userId }}</span>
            </div>
            <div class="detail-row">
              <span class="label">实名认证</span>
              <n-tag :type="userInfo.verified ? 'success' : 'warning'">
                {{ userInfo.verified ? '已认证' : '未认证' }}
              </n-tag>
            </div>
            <div class="detail-row">
              <span class="label">用户组</span>
              <n-tag type="info">{{ userInfo.userGroup }}</n-tag>
            </div>
            <div class="detail-row">
              <span class="label">注册时间</span>
              <span class="value">{{ userInfo.registerTime }}</span>
            </div>
            <div class="detail-row">
              <span class="label">注册邮箱</span>
              <span class="value">{{ userInfo.email }}</span>
            </div>
            <div class="detail-row">
              <span class="label">隧道数量</span>
              <span class="value">{{ userInfo.tunnelCount }}</span>
            </div>
            <div class="detail-row">
              <span class="label">剩余积分</span>
              <span class="value">{{ userInfo.points }}</span>
            </div>
            <div class="detail-row">
              <span class="label">入站带宽</span>
              <span class="value">{{ userInfo.inboundBandwidth }}</span>
            </div>
            <div class="detail-row">
              <span class="label">出站带宽</span>
              <span class="value">{{ userInfo.outboundBandwidth }}</span>
            </div>
          </div>
          
          <n-divider />
          
          <div class="access-key-section">
            <n-button
              v-if="!showAccessKey"
              @click="toggleAccessKey"
              type="primary"
              ghost
            >
              显示访问密钥
            </n-button>
            <div v-else class="access-key-display">
              <n-input
                :value="accessKey"
                readonly
                type="password"
                show-password-on="click"
              />
              <n-button @click="copyAccessKey" type="primary" ghost>
                复制
              </n-button>
            </div>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 实名认证卡片 -->
    <n-card title="实名认证" :bordered="false" class="verification-card">
      <div v-if="userInfo.verified" class="verified-status">
        <n-result status="success" title="已完成实名认证" description="您的账户已通过实名认证">
          <template #footer>
            <div class="verified-info">
              <p><strong>认证姓名：</strong>{{ verificationInfo.name }}</p>
              <p><strong>身份证号：</strong>{{ maskIdCard(verificationInfo.idCard) }}</p>
              <p><strong>认证时间：</strong>{{ verificationInfo.verifyTime }}</p>
            </div>
          </template>
        </n-result>
      </div>
      <div v-else class="unverified-status">
        <n-result status="info" title="未完成实名认证" description="完成实名认证后可享受更多服务">
          <template #footer>
            <n-button type="primary" @click="showVerificationModal = true">
              立即认证
            </n-button>
          </template>
        </n-result>
      </div>
    </n-card>

    <!-- 重置访问密钥确认弹窗 -->
    <n-modal v-model:show="showResetKeyModal" preset="dialog" type="error">
      <template #header>
        <div>警告</div>
      </template>
      <div>
        <p>重置访问密钥后旧的配置文件均无法使用，这代表着您的所有隧道需要重新获取配置文件再启动、且所有保存登录的设备均需重新登录。</p>
      </div>
      <template #action>
        <n-button @click="showResetKeyModal = false">取消</n-button>
        <n-button type="error" @click="handleResetAccessKey">确定</n-button>
      </template>
    </n-modal>

    <!-- 修改用户名弹窗 -->
    <n-modal v-model:show="showUsernameModal" preset="card" title="修改用户名" style="width: 400px;">
      <n-form ref="usernameFormRef" :model="usernameForm" :rules="usernameRules">
        <n-form-item path="username" label="新用户名">
          <n-input v-model:value="usernameForm.username" placeholder="请输入新用户名" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showUsernameModal = false">取消</n-button>
          <n-button type="primary" @click="handleUpdateUsername" :loading="updating">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 更改头像弹窗 -->
    <n-modal v-model:show="showAvatarModal" preset="card" title="更改头像" style="width: 400px;">
      <n-alert type="info" style="margin-bottom: 16px;">
        图片链接仅支持直链，且无反盗链的链接
      </n-alert>
      <n-form ref="avatarFormRef" :model="avatarForm" :rules="avatarRules">
        <n-form-item path="avatar" label="头像链接">
          <n-input v-model:value="avatarForm.avatar" placeholder="请输入图片链接" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showAvatarModal = false">取消</n-button>
          <n-button type="primary" @click="handleUpdateAvatar" :loading="updating">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 修改密码弹窗 -->
    <n-modal v-model:show="showPasswordModal" preset="card" title="修改密码" style="width: 400px;">
      <n-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules">
        <n-form-item path="oldPassword" label="原密码">
          <n-input
            v-model:value="passwordForm.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password-on="click"
          />
        </n-form-item>
        <n-form-item path="newPassword" label="新密码">
          <n-input
            v-model:value="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password-on="click"
          />
        </n-form-item>
        <n-form-item path="confirmPassword" label="确认新密码">
          <n-input
            v-model:value="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password-on="click"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showPasswordModal = false">取消</n-button>
          <n-button type="primary" @click="handleUpdatePassword" :loading="updating">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 更改邮箱弹窗 -->
    <n-modal v-model:show="showEmailModal" preset="card" title="更改邮箱" style="width: 400px;">
      <n-alert type="warning" style="margin-bottom: 16px;">
        更改邮箱后需要重新验证，请确保新邮箱可以正常接收邮件
      </n-alert>
      <n-form ref="emailFormRef" :model="emailForm" :rules="emailRules">
        <n-form-item path="newEmail" label="新邮箱">
          <n-input v-model:value="emailForm.newEmail" placeholder="请输入新邮箱地址" />
        </n-form-item>
        <n-form-item path="password" label="登录密码">
          <n-input
            v-model:value="emailForm.password"
            type="password"
            placeholder="请输入登录密码以确认身份"
            show-password-on="click"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEmailModal = false">取消</n-button>
          <n-button type="warning" @click="handleUpdateEmail" :loading="updating">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 删除账户确认弹窗 -->
    <n-modal v-model:show="showDeleteAccountModal" preset="dialog" type="error">
      <template #header>
        <div>危险操作</div>
      </template>
      <div>
        <p>删除账户是不可逆的操作，将会：</p>
        <ul>
          <li>永久删除您的所有数据</li>
          <li>删除所有隧道配置</li>
          <li>清空积分和使用记录</li>
          <li>无法恢复任何信息</li>
        </ul>
        <p><strong>请输入您的用户名以确认删除：</strong></p>
        <n-input
          v-model:value="deleteConfirmText"
          placeholder="请输入用户名"
          style="margin-top: 8px;"
        />
      </div>
      <template #action>
        <n-button @click="showDeleteAccountModal = false">取消</n-button>
        <n-button
          type="error"
          @click="handleDeleteAccount"
          :disabled="deleteConfirmText !== userInfo.username"
          :loading="updating"
        >
          确定删除
        </n-button>
      </template>
    </n-modal>

    <!-- 实名认证弹窗 -->
    <n-modal v-model:show="showVerificationModal" preset="card" title="实名认证" style="width: 500px;">
      <n-alert type="info" style="margin-bottom: 16px;">
        请填写真实信息，认证后无法修改
      </n-alert>
      <n-form ref="verificationFormRef" :model="verificationForm" :rules="verificationRules">
        <n-form-item path="name" label="真实姓名">
          <n-input v-model:value="verificationForm.name" placeholder="请输入真实姓名" />
        </n-form-item>
        <n-form-item path="idCard" label="身份证号">
          <n-input v-model:value="verificationForm.idCard" placeholder="请输入身份证号码" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showVerificationModal = false">取消</n-button>
          <n-button type="primary" @click="handleVerification" :loading="updating">提交认证</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMessage } from 'naive-ui'
import { CloseOutline } from '@vicons/ionicons5'

const message = useMessage()

// 响应式检测
const isMobile = ref(window.innerWidth <= 768)

// 用户信息
const userInfo = ref({
  username: localStorage.getItem('username') || '用户',
  userId: '12345',
  verified: false,
  userGroup: localStorage.getItem('group') || '普通用户',
  registerTime: '2024-01-01',
  email: '<EMAIL>',
  tunnelCount: 0,
  points: 100,
  inboundBandwidth: '10 Mbps',
  outboundBandwidth: '10 Mbps'
})

// 实名认证信息
const verificationInfo = ref({
  name: '张三',
  idCard: '123456789012345678',
  verifyTime: '2024-01-01 12:00:00'
})

// 访问密钥
const accessKey = ref('sk-1234567890abcdef')
const showAccessKey = ref(false)

// 弹窗状态
const showResetKeyModal = ref(false)
const showUsernameModal = ref(false)
const showAvatarModal = ref(false)
const showPasswordModal = ref(false)
const showEmailModal = ref(false)
const showDeleteAccountModal = ref(false)
const showVerificationModal = ref(false)

// 表单数据
const usernameForm = ref({ username: '' })
const avatarForm = ref({ avatar: '' })
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const emailForm = ref({
  newEmail: '',
  password: ''
})
const verificationForm = ref({
  name: '',
  idCard: ''
})
const deleteConfirmText = ref('')

// 加载状态
const updating = ref(false)

// 表单验证规则
const usernameRules = {
  username: {
    required: true,
    message: '请输入用户名',
    trigger: 'blur'
  }
}

const avatarRules = {
  avatar: {
    required: true,
    message: '请输入头像链接',
    trigger: 'blur'
  }
}

const passwordRules = {
  oldPassword: {
    required: true,
    message: '请输入原密码',
    trigger: 'blur'
  },
  newPassword: {
    required: true,
    message: '请输入新密码',
    trigger: 'blur'
  },
  confirmPassword: {
    required: true,
    message: '请确认新密码',
    trigger: 'blur',
    validator: (rule: any, value: string) => {
      return value === passwordForm.value.newPassword || new Error('两次输入的密码不一致')
    }
  }
}

const emailRules = {
  newEmail: {
    required: true,
    message: '请输入邮箱地址',
    trigger: 'blur'
  },
  password: {
    required: true,
    message: '请输入登录密码',
    trigger: 'blur'
  }
}

const verificationRules = {
  name: {
    required: true,
    message: '请输入真实姓名',
    trigger: 'blur'
  },
  idCard: {
    required: true,
    message: '请输入身份证号码',
    trigger: 'blur'
  }
}

// 切换访问密钥显示
const toggleAccessKey = () => {
  showAccessKey.value = !showAccessKey.value
}

// 复制访问密钥
const copyAccessKey = async () => {
  try {
    await navigator.clipboard.writeText(accessKey.value)
    message.success('访问密钥已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

// 重置访问密钥
const handleResetAccessKey = async () => {
  updating.value = true
  try {
    // 调用API重置访问密钥
    await new Promise(resolve => setTimeout(resolve, 1000))
    accessKey.value = 'sk-' + Math.random().toString(36).substring(2, 18)
    message.success('访问密钥重置成功')
    showResetKeyModal.value = false
  } catch (error) {
    message.error('重置失败')
  } finally {
    updating.value = false
  }
}

// 修改用户名
const handleUpdateUsername = async () => {
  updating.value = true
  try {
    // 调用API修改用户名
    await new Promise(resolve => setTimeout(resolve, 1000))
    userInfo.value.username = usernameForm.value.username
    localStorage.setItem('username', usernameForm.value.username)
    message.success('用户名修改成功')
    showUsernameModal.value = false
  } catch (error) {
    message.error('修改失败')
  } finally {
    updating.value = false
  }
}

// 更改头像
const handleUpdateAvatar = async () => {
  updating.value = true
  try {
    // 调用API更改头像
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('头像更改成功')
    showAvatarModal.value = false
  } catch (error) {
    message.error('更改失败')
  } finally {
    updating.value = false
  }
}

// 修改密码
const handleUpdatePassword = async () => {
  updating.value = true
  try {
    // 调用API修改密码
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('密码修改成功')
    showPasswordModal.value = false
    passwordForm.value = { oldPassword: '', newPassword: '', confirmPassword: '' }
  } catch (error) {
    message.error('修改失败')
  } finally {
    updating.value = false
  }
}

// 更改邮箱
const handleUpdateEmail = async () => {
  updating.value = true
  try {
    // 调用API更改邮箱
    await new Promise(resolve => setTimeout(resolve, 1000))
    userInfo.value.email = emailForm.value.newEmail
    message.success('邮箱更改成功，请查收验证邮件')
    showEmailModal.value = false
    emailForm.value = { newEmail: '', password: '' }
  } catch (error) {
    message.error('更改失败')
  } finally {
    updating.value = false
  }
}

// 删除账户
const handleDeleteAccount = async () => {
  updating.value = true
  try {
    // 调用API删除账户
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('账户删除成功')
    showDeleteAccountModal.value = false
    // 清除本地存储并跳转到登录页
    localStorage.clear()
    window.location.href = '/login'
  } catch (error) {
    message.error('删除失败')
  } finally {
    updating.value = false
  }
}

// 实名认证
const handleVerification = async () => {
  updating.value = true
  try {
    // 调用API提交实名认证
    await new Promise(resolve => setTimeout(resolve, 1000))
    userInfo.value.verified = true
    verificationInfo.value = {
      name: verificationForm.value.name,
      idCard: verificationForm.value.idCard,
      verifyTime: new Date().toLocaleString('zh-CN')
    }
    message.success('实名认证提交成功，请等待审核')
    showVerificationModal.value = false
    verificationForm.value = { name: '', idCard: '' }
  } catch (error) {
    message.error('提交失败')
  } finally {
    updating.value = false
  }
}

// 身份证号脱敏
const maskIdCard = (idCard: string) => {
  if (!idCard || idCard.length < 8) return idCard
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4)
}

onMounted(() => {
  // 监听窗口大小变化
  const handleResize = () => {
    isMobile.value = window.innerWidth <= 768
  }
  window.addEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.profile-page {
  .setting-item {
    padding: 16px 0;
    border-bottom: 1px solid var(--n-border-color);
    
    &:last-child {
      border-bottom: none;
    }
    
    .setting-header {
      margin-bottom: 12px;
      
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--n-text-color);
      }
      
      .setting-desc {
        margin: 0;
        font-size: 14px;
        color: var(--n-text-color-3);
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    .delete-btn {
      color: var(--n-error-color);
      
      &:hover {
        background-color: var(--n-error-color-hover);
      }
    }
  }
  
  .account-details {
    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--n-border-color);
      
      &:last-child {
        border-bottom: none;
      }
      
      .label {
        font-weight: 500;
        color: var(--n-text-color-2);
      }
      
      .value {
        color: var(--n-text-color);
      }
    }
  }
  
  .access-key-section {
    .access-key-display {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }
  
  .verification-card {
    margin-top: 16px;
    
    .verified-info {
      text-align: left;
      
      p {
        margin: 4px 0;
        
        strong {
          color: var(--n-text-color);
        }
      }
    }
  }
}
</style>
