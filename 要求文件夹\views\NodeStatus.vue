<template>
  <div class="node-status">
    <div class="page-header">
      <h2>节点状态</h2>
      <n-button @click="refreshNodes" :loading="loading">
        <n-icon size="16">
          <RefreshOutline />
        </n-icon>
        刷新
      </n-button>
    </div>

    <!-- 节点概览 -->
    <n-grid :cols="isMobile ? 2 : 4" :x-gap="16" :y-gap="16" class="overview-grid">
      <n-grid-item>
        <n-statistic label="总节点数" :value="nodeStats.total">
          <template #prefix>
            <n-icon size="20" color="#18a058">
              <ServerOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-grid-item>
      <n-grid-item>
        <n-statistic label="在线节点" :value="nodeStats.online">
          <template #prefix>
            <n-icon size="20" color="#52c41a">
              <CheckmarkCircleOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-grid-item>
      <n-grid-item>
        <n-statistic label="离线节点" :value="nodeStats.offline">
          <template #prefix>
            <n-icon size="20" color="#ff4d4f">
              <CloseCircleOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-grid-item>
      <n-grid-item>
        <n-statistic label="平均延迟" :value="nodeStats.avgLatency" suffix="ms">
          <template #prefix>
            <n-icon size="20" color="#1890ff">
              <SpeedometerOutline />
            </n-icon>
          </template>
        </n-statistic>
      </n-grid-item>
    </n-grid>

    <!-- 节点列表 -->
    <n-card :bordered="false" class="nodes-card">
      <template #header>
        <div class="card-header">
          <span>节点列表</span>
          <n-space>
            <n-select
              v-model:value="regionFilter"
              placeholder="地区筛选"
              :options="regionOptions"
              clearable
              style="width: 120px;"
            />
            <n-select
              v-model:value="statusFilter"
              placeholder="状态筛选"
              :options="statusOptions"
              clearable
              style="width: 120px;"
            />
          </n-space>
        </div>
      </template>

      <div class="nodes-grid">
        <div
          v-for="node in filteredNodes"
          :key="node.id"
          class="node-card"
          :class="{ 'node-offline': node.status === 'offline' }"
        >
          <div class="node-header">
            <div class="node-info">
              <h4 class="node-name">{{ node.name }}</h4>
              <n-tag :type="getStatusType(node.status)" size="small">
                {{ getStatusText(node.status) }}
              </n-tag>
            </div>
            <div class="node-actions">
              <n-button
                size="small"
                @click="testLatency(node)"
                :loading="node.testing"
                ghost
              >
                测速
              </n-button>
            </div>
          </div>

          <div class="node-details">
            <div class="detail-row">
              <span class="label">地区</span>
              <span class="value">{{ node.region }}</span>
            </div>
            <div class="detail-row">
              <span class="label">延迟</span>
              <span class="value" :class="getLatencyClass(node.latency)">
                {{ node.latency ? `${node.latency}ms` : '-' }}
              </span>
            </div>
            <div class="detail-row">
              <span class="label">负载</span>
              <div class="load-bar">
                <n-progress
                  type="line"
                  :percentage="node.load"
                  :color="getLoadColor(node.load)"
                  :show-indicator="false"
                  :height="6"
                />
                <span class="load-text">{{ node.load }}%</span>
              </div>
            </div>
            <div class="detail-row">
              <span class="label">在线隧道</span>
              <span class="value">{{ node.activeTunnels }}</span>
            </div>
            <div class="detail-row">
              <span class="label">带宽使用</span>
              <span class="value">{{ formatBandwidth(node.bandwidth) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">最后更新</span>
              <span class="value">{{ formatTime(node.lastUpdate) }}</span>
            </div>
          </div>

          <!-- 节点图表 -->
          <div class="node-chart" v-if="node.status === 'online'">
            <div class="chart-header">
              <span>24小时延迟趋势</span>
            </div>
            <div class="chart-placeholder">
              <n-icon size="24" color="#ccc">
                <StatsChartOutline />
              </n-icon>
              <span>图表数据</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <n-empty
        v-if="filteredNodes.length === 0"
        description="没有找到符合条件的节点"
        style="margin: 40px 0;"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import {
  RefreshOutline,
  ServerOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  SpeedometerOutline,
  StatsChartOutline
} from '@vicons/ionicons5'

const message = useMessage()

// 响应式检测
const isMobile = ref(window.innerWidth <= 768)

// 数据状态
const loading = ref(false)
const nodes = ref([])
const regionFilter = ref(null)
const statusFilter = ref(null)

// 节点统计
const nodeStats = computed(() => {
  const total = nodes.value.length
  const online = nodes.value.filter((node: any) => node.status === 'online').length
  const offline = total - online
  const avgLatency = Math.round(
    nodes.value
      .filter((node: any) => node.status === 'online' && node.latency)
      .reduce((sum: number, node: any) => sum + node.latency, 0) /
    Math.max(1, nodes.value.filter((node: any) => node.status === 'online' && node.latency).length)
  )

  return { total, online, offline, avgLatency }
})

// 筛选选项
const regionOptions = [
  { label: '香港', value: 'HK' },
  { label: '美国', value: 'US' },
  { label: '日本', value: 'JP' },
  { label: '新加坡', value: 'SG' }
]

const statusOptions = [
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' },
  { label: '维护中', value: 'maintenance' }
]

// 过滤后的节点列表
const filteredNodes = computed(() => {
  let result = nodes.value

  if (regionFilter.value) {
    result = result.filter((node: any) => node.region === regionFilter.value)
  }

  if (statusFilter.value) {
    result = result.filter((node: any) => node.status === statusFilter.value)
  }

  return result
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap = {
    online: 'success',
    offline: 'error',
    maintenance: 'warning'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中'
  }
  return statusMap[status] || '未知'
}

// 获取延迟样式类
const getLatencyClass = (latency: number) => {
  if (!latency) return ''
  if (latency < 50) return 'latency-good'
  if (latency < 100) return 'latency-normal'
  return 'latency-bad'
}

// 获取负载颜色
const getLoadColor = (load: number) => {
  if (load < 50) return '#52c41a'
  if (load < 80) return '#faad14'
  return '#ff4d4f'
}

// 格式化带宽
const formatBandwidth = (bandwidth: number) => {
  if (bandwidth < 1024) return `${bandwidth} KB/s`
  return `${(bandwidth / 1024).toFixed(1)} MB/s`
}

// 格式化时间
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 刷新节点列表
const refreshNodes = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    nodes.value = [
      {
        id: 'hk01',
        name: '香港节点 HK01',
        region: 'HK',
        status: 'online',
        latency: 45,
        load: 35,
        activeTunnels: 128,
        bandwidth: 2048,
        lastUpdate: new Date().toISOString(),
        testing: false
      },
      {
        id: 'us01',
        name: '美国节点 US01',
        region: 'US',
        status: 'online',
        latency: 180,
        load: 68,
        activeTunnels: 89,
        bandwidth: 1536,
        lastUpdate: new Date().toISOString(),
        testing: false
      },
      {
        id: 'jp01',
        name: '日本节点 JP01',
        region: 'JP',
        status: 'offline',
        latency: null,
        load: 0,
        activeTunnels: 0,
        bandwidth: 0,
        lastUpdate: new Date(Date.now() - 300000).toISOString(),
        testing: false
      },
      {
        id: 'sg01',
        name: '新加坡节点 SG01',
        region: 'SG',
        status: 'maintenance',
        latency: null,
        load: 0,
        activeTunnels: 0,
        bandwidth: 0,
        lastUpdate: new Date(Date.now() - 600000).toISOString(),
        testing: false
      }
    ]
  } catch (error) {
    message.error('加载节点状态失败')
  } finally {
    loading.value = false
  }
}

// 测试延迟
const testLatency = async (node: any) => {
  node.testing = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    if (node.status === 'online') {
      node.latency = Math.floor(Math.random() * 200) + 20
      message.success(`${node.name} 延迟测试完成`)
    } else {
      message.warning(`${node.name} 当前离线，无法测试延迟`)
    }
  } catch (error) {
    message.error('延迟测试失败')
  } finally {
    node.testing = false
  }
}

onMounted(() => {
  refreshNodes()
  
  // 监听窗口大小变化
  const handleResize = () => {
    isMobile.value = window.innerWidth <= 768
  }
  window.addEventListener('resize', handleResize)
  
  // 定时刷新节点状态
  const interval = setInterval(refreshNodes, 30000)
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style lang="scss" scoped>
.node-status {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h2 {
      margin: 0;
      color: var(--n-text-color);
    }
  }
  
  .overview-grid {
    margin-bottom: 24px;
  }
  
  .nodes-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
  }
  
  .nodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 16px;
  }
  
  .node-card {
    border: 1px solid var(--n-border-color);
    border-radius: 6px;
    padding: 16px;
    background: var(--n-card-color);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &.node-offline {
      opacity: 0.7;
      background: var(--n-card-color-modal);
    }
    
    .node-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      
      .node-info {
        .node-name {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 500;
          color: var(--n-text-color);
        }
      }
    }
    
    .node-details {
      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        
        .label {
          color: var(--n-text-color-2);
          font-size: 14px;
        }
        
        .value {
          color: var(--n-text-color);
          font-size: 14px;
          
          &.latency-good {
            color: #52c41a;
          }
          
          &.latency-normal {
            color: #faad14;
          }
          
          &.latency-bad {
            color: #ff4d4f;
          }
        }
        
        .load-bar {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          max-width: 120px;
          
          .load-text {
            font-size: 12px;
            color: var(--n-text-color-2);
            min-width: 30px;
          }
        }
      }
    }
    
    .node-chart {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--n-border-color);
      
      .chart-header {
        font-size: 14px;
        color: var(--n-text-color-2);
        margin-bottom: 8px;
      }
      
      .chart-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 80px;
        color: var(--n-text-color-3);
        font-size: 12px;
        gap: 4px;
      }
    }
  }
}

@media (max-width: 768px) {
  .node-status {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      
      h2 {
        text-align: center;
      }
    }
    
    .nodes-grid {
      grid-template-columns: 1fr;
    }
    
    .nodes-card .card-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }
  }
}
</style>
