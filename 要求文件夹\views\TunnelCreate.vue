<template>
  <div class="tunnel-create">
    <div class="page-header">
      <h2>创建隧道</h2>
      <n-button @click="$router.back()">
        <n-icon size="16">
          <ArrowBackOutline />
        </n-icon>
        返回
      </n-button>
    </div>

    <n-card :bordered="false">
      <n-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-placement="left"
        label-width="120px"
        require-mark-placement="right-hanging"
      >
        <n-grid :cols="isMobile ? 1 : 2" :x-gap="24" :y-gap="16">
          <n-grid-item>
            <n-form-item path="name" label="隧道名称">
              <n-input
                v-model:value="form.name"
                placeholder="请输入隧道名称（英文字母、数字、下划线）"
                @input="validateName"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item path="type" label="隧道类型">
              <n-select
                v-model:value="form.type"
                :options="typeOptions"
                placeholder="请选择隧道类型"
                @update:value="onTypeChange"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item path="nodeId" label="节点选择">
              <n-select
                v-model:value="form.nodeId"
                :options="nodeOptions"
                placeholder="请选择节点"
                :loading="loadingNodes"
                @update:value="onNodeChange"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item path="localIp" label="本地IP">
              <n-input
                v-model:value="form.localIp"
                placeholder="请输入本地IP地址"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item path="localPort" label="本地端口">
              <n-input-number
                v-model:value="form.localPort"
                placeholder="请输入本地端口"
                :min="1"
                :max="65535"
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item path="remotePort" label="远程端口">
              <div class="remote-port-section">
                <n-input-number
                  v-if="form.type !== 'http' && form.type !== 'https'"
                  v-model:value="form.remotePort"
                  placeholder="请输入远程端口"
                  :min="1"
                  :max="65535"
                  style="width: 100%"
                />
                <n-input
                  v-else
                  v-model:value="form.customDomain"
                  placeholder="请输入自定义域名（可选）"
                  style="width: 100%"
                />
                <n-button
                  v-if="form.type !== 'http' && form.type !== 'https'"
                  @click="getRandomPort"
                  :loading="gettingPort"
                  size="small"
                  type="primary"
                  ghost
                >
                  随机端口
                </n-button>
              </div>
            </n-form-item>
          </n-grid-item>

          <n-grid-item :span="isMobile ? 1 : 2">
            <n-form-item path="remark" label="备注信息">
              <n-input
                v-model:value="form.remark"
                type="textarea"
                placeholder="请输入备注信息（可选）"
                :autosize="{ minRows: 2, maxRows: 4 }"
              />
            </n-form-item>
          </n-grid-item>

          <!-- HTTP/HTTPS 特殊配置 -->
          <n-grid-item v-if="form.type === 'http' || form.type === 'https'" :span="isMobile ? 1 : 2">
            <n-card title="HTTP/HTTPS 配置" size="small">
              <n-space vertical>
                <n-checkbox v-model:checked="form.httpAuth">启用HTTP基础认证</n-checkbox>
                <div v-if="form.httpAuth" class="auth-config">
                  <n-space>
                    <n-input
                      v-model:value="form.httpUser"
                      placeholder="用户名"
                      style="width: 150px"
                    />
                    <n-input
                      v-model:value="form.httpPassword"
                      type="password"
                      placeholder="密码"
                      style="width: 150px"
                      show-password-on="click"
                    />
                  </n-space>
                </div>
                <n-checkbox v-model:checked="form.hostHeaderRewrite">重写Host头</n-checkbox>
                <n-input
                  v-if="form.hostHeaderRewrite"
                  v-model:value="form.hostHeader"
                  placeholder="请输入Host头内容"
                />
              </n-space>
            </n-card>
          </n-grid-item>

          <!-- 高级配置 -->
          <n-grid-item :span="isMobile ? 1 : 2">
            <n-card title="高级配置" size="small">
              <n-space vertical>
                <n-checkbox v-model:checked="form.useEncryption">启用加密</n-checkbox>
                <n-checkbox v-model:checked="form.useCompression">启用压缩</n-checkbox>
                <div class="bandwidth-limit">
                  <n-checkbox v-model:checked="form.bandwidthLimit">带宽限制</n-checkbox>
                  <n-input-number
                    v-if="form.bandwidthLimit"
                    v-model:value="form.maxBandwidth"
                    placeholder="KB/s"
                    :min="1"
                    style="width: 120px; margin-left: 8px"
                  />
                </div>
              </n-space>
            </n-card>
          </n-grid-item>
        </n-grid>

        <n-divider />

        <div class="form-actions">
          <n-space justify="center">
            <n-button @click="$router.back()">取消</n-button>
            <n-button type="primary" @click="handleSubmit" :loading="creating">
              创建隧道
            </n-button>
          </n-space>
        </div>
      </n-form>
    </n-card>

    <!-- 创建成功弹窗 -->
    <n-modal v-model:show="showSuccessModal" preset="card" title="创建成功" style="width: 600px;">
      <n-result status="success" title="隧道创建成功" :description="`隧道 ${createdTunnel.name} 已成功创建`">
        <template #footer>
          <n-space vertical>
            <div class="tunnel-info">
              <p><strong>隧道名称：</strong>{{ createdTunnel.name }}</p>
              <p><strong>访问地址：</strong>{{ createdTunnel.accessUrl }}</p>
              <p v-if="createdTunnel.customDomain"><strong>自定义域名：</strong>{{ createdTunnel.customDomain }}</p>
            </div>
            <n-space justify="center">
              <n-button @click="$router.push({ name: 'tunnel-list' })">查看隧道列表</n-button>
              <n-button type="primary" @click="createAnother">继续创建</n-button>
            </n-space>
          </n-space>
        </template>
      </n-result>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { ArrowBackOutline } from '@vicons/ionicons5'

const router = useRouter()
const message = useMessage()

// 响应式检测
const isMobile = ref(window.innerWidth <= 768)

// 表单数据
const form = ref({
  name: '',
  type: '',
  nodeId: '',
  localIp: '127.0.0.1',
  localPort: null,
  remotePort: null,
  customDomain: '',
  remark: '',
  httpAuth: false,
  httpUser: '',
  httpPassword: '',
  hostHeaderRewrite: false,
  hostHeader: '',
  useEncryption: false,
  useCompression: false,
  bandwidthLimit: false,
  maxBandwidth: null
})

// 状态
const creating = ref(false)
const loadingNodes = ref(false)
const gettingPort = ref(false)
const showSuccessModal = ref(false)

// 创建成功的隧道信息
const createdTunnel = ref({
  name: '',
  accessUrl: '',
  customDomain: ''
})

// 隧道类型选项
const typeOptions = [
  { label: 'HTTP', value: 'http' },
  { label: 'HTTPS', value: 'https' },
  { label: 'TCP', value: 'tcp' },
  { label: 'UDP', value: 'udp' }
]

// 节点选项
const nodeOptions = ref([
  { label: '香港节点 - HK01', value: 'hk01' },
  { label: '美国节点 - US01', value: 'us01' },
  { label: '日本节点 - JP01', value: 'jp01' }
])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入隧道名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '隧道名称只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  type: { required: true, message: '请选择隧道类型', trigger: 'change' },
  nodeId: { required: true, message: '请选择节点', trigger: 'change' },
  localIp: { required: true, message: '请输入本地IP地址', trigger: 'blur' },
  localPort: { required: true, type: 'number', message: '请输入本地端口', trigger: 'blur' },
  remotePort: {
    required: computed(() => form.value.type !== 'http' && form.value.type !== 'https'),
    type: 'number',
    message: '请输入远程端口',
    trigger: 'blur'
  }
}

// 验证隧道名称
const validateName = (value: string) => {
  form.value.name = value.toLowerCase().replace(/[^a-z0-9_-]/g, '')
}

// 隧道类型变化处理
const onTypeChange = (value: string) => {
  if (value === 'http' || value === 'https') {
    form.value.remotePort = null
  }
}

// 节点变化处理
const onNodeChange = (value: string) => {
  // 可以根据节点加载可用端口等信息
}

// 获取随机端口
const getRandomPort = async () => {
  gettingPort.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    form.value.remotePort = Math.floor(Math.random() * (65535 - 10000) + 10000)
    message.success('已分配随机端口')
  } catch (error) {
    message.error('获取随机端口失败')
  } finally {
    gettingPort.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  creating.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 生成访问地址
    let accessUrl = ''
    if (form.value.type === 'http') {
      accessUrl = form.value.customDomain || `http://${form.value.nodeId}.example.com:${form.value.name}`
    } else if (form.value.type === 'https') {
      accessUrl = form.value.customDomain || `https://${form.value.nodeId}.example.com:${form.value.name}`
    } else {
      accessUrl = `${form.value.nodeId}.example.com:${form.value.remotePort}`
    }
    
    createdTunnel.value = {
      name: form.value.name,
      accessUrl,
      customDomain: form.value.customDomain
    }
    
    showSuccessModal.value = true
    message.success('隧道创建成功')
  } catch (error) {
    message.error('创建失败，请重试')
  } finally {
    creating.value = false
  }
}

// 继续创建
const createAnother = () => {
  showSuccessModal.value = false
  // 重置表单
  form.value = {
    name: '',
    type: '',
    nodeId: '',
    localIp: '127.0.0.1',
    localPort: null,
    remotePort: null,
    customDomain: '',
    remark: '',
    httpAuth: false,
    httpUser: '',
    httpPassword: '',
    hostHeaderRewrite: false,
    hostHeader: '',
    useEncryption: false,
    useCompression: false,
    bandwidthLimit: false,
    maxBandwidth: null
  }
}

onMounted(() => {
  // 监听窗口大小变化
  const handleResize = () => {
    isMobile.value = window.innerWidth <= 768
  }
  window.addEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.tunnel-create {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h2 {
      margin: 0;
      color: var(--n-text-color);
    }
  }
  
  .remote-port-section {
    display: flex;
    gap: 8px;
    align-items: center;
  }
  
  .auth-config {
    margin-top: 8px;
  }
  
  .bandwidth-limit {
    display: flex;
    align-items: center;
  }
  
  .form-actions {
    margin-top: 24px;
  }
  
  .tunnel-info {
    background: var(--n-card-color);
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    
    p {
      margin: 8px 0;
      
      strong {
        color: var(--n-text-color);
      }
    }
  }
}

@media (max-width: 768px) {
  .tunnel-create {
    .page-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
      
      h2 {
        text-align: center;
      }
    }
    
    .remote-port-section {
      flex-direction: column;
      align-items: stretch;
    }
  }
}
</style>
